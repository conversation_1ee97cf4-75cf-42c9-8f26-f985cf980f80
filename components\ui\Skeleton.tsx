import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle } from 'react-native';
import { theme } from '@/constants/theme';

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  variant?: 'text' | 'rectangular' | 'circular';
}

export default function Skeleton({
  width = '100%',
  height = 20,
  borderRadius = theme.borderRadius.sm,
  style,
  variant = 'rectangular',
}: SkeletonProps) {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    let isMounted = true;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    if (isMounted) {
      animation.start();
    }

    return () => {
      isMounted = false;
      animation.stop();
    };
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.gray200, theme.colors.gray300],
  });

  const getVariantStyle = (): any => {
    switch (variant) {
      case 'text':
        return { height: 16, borderRadius: 4 };
      case 'circular':
        return {
          width: height,
          height,
          borderRadius: height / 2
        };
      case 'rectangular':
      default:
        return { width, height, borderRadius };
    }
  };

  return (
    <Animated.View
      style={[
        styles.skeleton,
        getVariantStyle(),
        { backgroundColor },
        style,
      ]}
    />
  );
}

// Skeleton components for common use cases
export function SkeletonCard({ style }: { style?: ViewStyle }) {
  return (
    <View style={[styles.card, style]}>
      <Skeleton variant="rectangular" height={200} />
      <View style={styles.cardContent}>
        <Skeleton variant="text" width="60%" />
        <Skeleton variant="text" width="40%" />
        <View style={styles.cardFooter}>
          <Skeleton variant="circular" height={24} />
          <Skeleton variant="text" width="30%" />
        </View>
      </View>
    </View>
  );
}

export function SkeletonProfile({ style }: { style?: ViewStyle }) {
  return (
    <View style={[styles.profile, style]}>
      <Skeleton variant="circular" height={80} />
      <View style={styles.profileContent}>
        <Skeleton variant="text" width="50%" />
        <Skeleton variant="text" width="70%" />
        <Skeleton variant="text" width="40%" />
      </View>
    </View>
  );
}

export function SkeletonMessage({ style }: { style?: ViewStyle }) {
  return (
    <View style={[styles.message, style]}>
      <Skeleton variant="circular" height={40} />
      <View style={styles.messageContent}>
        <Skeleton variant="text" width="30%" />
        <Skeleton variant="text" width="80%" />
      </View>
      <Skeleton variant="text" width="20%" />
    </View>
  );
}

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: theme.colors.gray200,
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
    overflow: 'hidden',
    marginBottom: theme.spacing.md,
  },
  cardContent: {
    padding: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    marginTop: theme.spacing.sm,
  },
  profile: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    gap: theme.spacing.md,
  },
  profileContent: {
    flex: 1,
    gap: theme.spacing.sm,
  },
  message: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    gap: theme.spacing.md,
  },
  messageContent: {
    flex: 1,
    gap: theme.spacing.xs,
  },
});
