import AsyncStorage from '@react-native-async-storage/async-storage';

export interface TranslationResult {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
}

export interface LanguageDetectionResult {
  language: string;
  confidence: number;
}

export interface TranslationSettings {
  autoTranslate: boolean;
  targetLanguage: string;
  showOriginal: boolean;
}

// Supported languages
export const SUPPORTED_LANGUAGES = {
  'en': 'English',
  'es': 'Spanish',
  'fr': 'French',
  'de': 'German',
  'it': 'Italian',
  'pt': 'Portuguese',
  'ru': 'Russian',
  'ja': 'Japanese',
  'ko': 'Korean',
  'zh': 'Chinese',
  'ar': 'Arabic',
  'hi': 'Hindi',
  'tr': 'Turkish',
  'pl': 'Polish',
  'nl': 'Dutch',
  'sv': 'Swedish',
  'da': 'Danish',
  'no': 'Norwegian',
  'fi': 'Finnish',
  'cs': 'Czech',
  'hu': 'Hungarian',
  'ro': 'Romanian',
  'bg': 'Bulgarian',
  'hr': 'Croatian',
  'sk': 'Slovak',
  'sl': 'Slovenian',
  'et': 'Estonian',
  'lv': 'Latvian',
  'lt': 'Lithuanian',
  'mt': 'Maltese',
  'ga': 'Irish',
  'cy': 'Welsh',
  'eu': 'Basque',
  'ca': 'Catalan',
  'gl': 'Galician',
  'is': 'Icelandic',
  'mk': 'Macedonian',
  'sq': 'Albanian',
  'sr': 'Serbian',
  'bs': 'Bosnian',
  'me': 'Montenegrin',
  'uk': 'Ukrainian',
  'be': 'Belarusian',
  'kk': 'Kazakh',
  'ky': 'Kyrgyz',
  'uz': 'Uzbek',
  'tg': 'Tajik',
  'mn': 'Mongolian',
  'ka': 'Georgian',
  'hy': 'Armenian',
  'az': 'Azerbaijani',
  'he': 'Hebrew',
  'fa': 'Persian',
  'ur': 'Urdu',
  'bn': 'Bengali',
  'ta': 'Tamil',
  'te': 'Telugu',
  'ml': 'Malayalam',
  'kn': 'Kannada',
  'gu': 'Gujarati',
  'pa': 'Punjabi',
  'or': 'Odia',
  'as': 'Assamese',
  'ne': 'Nepali',
  'si': 'Sinhala',
  'my': 'Myanmar',
  'km': 'Khmer',
  'lo': 'Lao',
  'th': 'Thai',
  'vi': 'Vietnamese',
  'id': 'Indonesian',
  'ms': 'Malay',
  'tl': 'Filipino',
  'sw': 'Swahili',
  'am': 'Amharic',
  'yo': 'Yoruba',
  'ig': 'Igbo',
  'ha': 'Hausa',
  'zu': 'Zulu',
  'af': 'Afrikaans',
  'xh': 'Xhosa',
  'st': 'Sesotho',
  'tn': 'Setswana',
  'ss': 'Siswati',
  've': 'Venda',
  'ts': 'Tsonga',
  'nr': 'Ndebele',
};

export class TranslationService {
  private static instance: TranslationService;
  private readonly SETTINGS_KEY = 'translation_settings';
  private readonly CACHE_KEY = 'translation_cache';
  private readonly API_KEY = 'your-google-translate-api-key'; // Replace with your API key
  private cache: Map<string, TranslationResult> = new Map();

  private constructor() {
    this.loadCache();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  // Load cached translations
  private async loadCache(): Promise<void> {
    try {
      const cachedData = await AsyncStorage.getItem(this.CACHE_KEY);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.cache = new Map(parsed);
      }
    } catch (error) {
      console.warn('Failed to load translation cache:', error);
    }
  }

  // Save cache to storage
  private async saveCache(): Promise<void> {
    try {
      const cacheArray = Array.from(this.cache.entries());
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheArray));
    } catch (error) {
      console.warn('Failed to save translation cache:', error);
    }
  }

  // Detect language of text
  public async detectLanguage(text: string): Promise<LanguageDetectionResult> {
    try {
      // For demo purposes, we'll use a simple heuristic
      // In production, use Google Translate API or similar
      const detectedLang = this.simpleLanguageDetection(text);
      
      return {
        language: detectedLang,
        confidence: 0.85,
      };
    } catch (error) {
      console.error('Language detection failed:', error);
      return {
        language: 'en',
        confidence: 0.5,
      };
    }
  }

  // Simple language detection (for demo)
  private simpleLanguageDetection(text: string): string {
    // Basic patterns for common languages
    const patterns = {
      'es': /[ñáéíóúü]/i,
      'fr': /[àâäéèêëïîôöùûüÿç]/i,
      'de': /[äöüß]/i,
      'it': /[àèéìíîòóù]/i,
      'pt': /[ãâáàçéêíóôõú]/i,
      'ru': /[а-яё]/i,
      'ja': /[ひらがなカタカナ漢字]/,
      'ko': /[가-힣]/,
      'zh': /[一-龯]/,
      'ar': /[ا-ي]/,
    };

    for (const [lang, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        return lang;
      }
    }

    return 'en'; // Default to English
  }

  // Translate text
  public async translateText(
    text: string,
    targetLanguage: string,
    sourceLanguage?: string
  ): Promise<TranslationResult> {
    const cacheKey = `${text}-${sourceLanguage || 'auto'}-${targetLanguage}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      // Detect source language if not provided
      if (!sourceLanguage) {
        const detection = await this.detectLanguage(text);
        sourceLanguage = detection.language;
      }

      // Skip translation if source and target are the same
      if (sourceLanguage === targetLanguage) {
        return {
          originalText: text,
          translatedText: text,
          sourceLanguage,
          targetLanguage,
          confidence: 1.0,
        };
      }

      // For demo purposes, we'll simulate translation
      // In production, use Google Translate API or similar
      const translatedText = await this.mockTranslation(text, sourceLanguage, targetLanguage);

      const result: TranslationResult = {
        originalText: text,
        translatedText,
        sourceLanguage,
        targetLanguage,
        confidence: 0.9,
      };

      // Cache the result
      this.cache.set(cacheKey, result);
      this.saveCache();

      return result;
    } catch (error) {
      console.error('Translation failed:', error);
      throw new Error('Translation service unavailable');
    }
  }

  // Mock translation for demo (replace with real API)
  private async mockTranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock translations for demo
    const mockTranslations: { [key: string]: string } = {
      'Hello': 'Hola',
      'How are you?': '¿Cómo estás?',
      'Good morning': 'Buenos días',
      'Thank you': 'Gracias',
      'I love you': 'Te amo',
      'Want to grab coffee?': '¿Quieres tomar un café?',
      'That sounds great!': '¡Eso suena genial!',
      'See you soon': 'Nos vemos pronto',
    };

    return mockTranslations[text] || `[${targetLanguage.toUpperCase()}] ${text}`;
  }

  // Get translation settings
  public async getSettings(): Promise<TranslationSettings> {
    try {
      const settings = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (settings) {
        return JSON.parse(settings);
      }
    } catch (error) {
      console.warn('Failed to load translation settings:', error);
    }

    // Default settings
    return {
      autoTranslate: false,
      targetLanguage: 'en',
      showOriginal: true,
    };
  }

  // Save translation settings
  public async saveSettings(settings: TranslationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save translation settings:', error);
    }
  }

  // Clear translation cache
  public async clearCache(): Promise<void> {
    try {
      this.cache.clear();
      await AsyncStorage.removeItem(this.CACHE_KEY);
    } catch (error) {
      console.error('Failed to clear translation cache:', error);
    }
  }

  // Get supported languages
  public getSupportedLanguages(): { [key: string]: string } {
    return SUPPORTED_LANGUAGES;
  }

  // Check if language is supported
  public isLanguageSupported(languageCode: string): boolean {
    return languageCode in SUPPORTED_LANGUAGES;
  }
}

// Export singleton instance
export const translationService = TranslationService.getInstance();
