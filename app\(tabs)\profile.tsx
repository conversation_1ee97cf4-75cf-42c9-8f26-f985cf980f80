import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Settings, 
  Edit, 
  Camera, 
  Heart, 
  Shield, 
  Bell, 
  HelpCircle, 
  LogOut,
  Star,
  Crown,
  MapPin,
  Briefcase,
  GraduationCap,
  ChevronRight
} from 'lucide-react-native';
import { useRouter } from 'expo-router';

// Mock user data
const USER_DATA = {
  name: '<PERSON>',
  age: 28,
  bio: 'Adventure seeker, coffee enthusiast, and weekend hiker. Looking for someone to explore the city with! 🌟',
  photos: [
    'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
    'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
  ],
  location: 'New York, NY',
  occupation: 'Marketing Manager',
  education: 'Columbia University',
  interests: ['Travel', 'Coffee', 'Hiking', 'Photography'],
  verified: true,
  isPremium: false,
};

const SETTINGS_SECTIONS = [
  {
    title: 'Account',
    items: [
      { icon: Edit, label: 'Edit Profile', action: 'edit_profile' },
      { icon: Camera, label: 'Manage Photos', action: 'manage_photos' },
      { icon: Shield, label: 'Privacy Settings', action: 'privacy' },
      { icon: Bell, label: 'Notifications', action: 'notifications' },
    ],
  },
  {
    title: 'Premium',
    items: [
      { icon: Crown, label: 'Upgrade to Premium', action: 'upgrade', highlight: true },
      { icon: Heart, label: 'My Likes & Matches', action: 'likes_matches' },
    ],
  },
  {
    title: 'Support',
    items: [
      { icon: HelpCircle, label: 'Help & Support', action: 'help' },
      { icon: Settings, label: 'App Settings', action: 'app_settings' },
    ],
  },
];

export default function ProfileTab() {
  const router = useRouter();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const handleSettingPress = (action: string) => {
    switch (action) {
      case 'edit_profile':
        Alert.alert('Edit Profile', 'Profile editing feature coming soon!');
        break;
      case 'manage_photos':
        Alert.alert('Manage Photos', 'Photo management feature coming soon!');
        break;
      case 'privacy':
        Alert.alert('Privacy Settings', 'Privacy settings feature coming soon!');
        break;
      case 'notifications':
        Alert.alert('Notifications', 'Notification settings feature coming soon!');
        break;
      case 'upgrade':
        router.push('/(tabs)/premium');
        break;
      case 'likes_matches':
        router.push('/(tabs)/likes');
        break;
      case 'help':
        Alert.alert('Help & Support', 'Help center feature coming soon!');
        break;
      case 'app_settings':
        Alert.alert('App Settings', 'App settings feature coming soon!');
        break;
      case 'logout':
        Alert.alert(
          'Sign Out',
          'Are you sure you want to sign out?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Sign Out', style: 'destructive', onPress: () => router.replace('/auth') },
          ]
        );
        break;
    }
  };

  const renderSettingItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={[styles.settingItem, item.highlight && styles.highlightedSetting]}
      onPress={() => handleSettingPress(item.action)}
    >
      <View style={styles.settingLeft}>
        <View style={[styles.settingIcon, item.highlight && styles.highlightedIcon]}>
          <item.icon 
            size={20} 
            color={item.highlight ? '#FFD700' : 'rgba(255, 255, 255, 0.8)'} 
          />
        </View>
        <Text style={[styles.settingLabel, item.highlight && styles.highlightedLabel]}>
          {item.label}
        </Text>
      </View>
      <ChevronRight size={20} color="rgba(255, 255, 255, 0.5)" />
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.title}>Profile</Text>
            <TouchableOpacity 
              style={styles.settingsButton}
              onPress={() => handleSettingPress('app_settings')}
            >
              <Settings size={24} color="white" />
            </TouchableOpacity>
          </View>

          <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.avatarContainer}>
                <Image source={{ uri: USER_DATA.photos[0] }} style={styles.avatar} />
                {USER_DATA.verified && (
                  <View style={styles.verifiedBadge}>
                    <Star size={16} color="white" fill="white" />
                  </View>
                )}
                {USER_DATA.isPremium && (
                  <View style={styles.premiumBadge}>
                    <Crown size={14} color="#FFD700" />
                  </View>
                )}
              </View>
              
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>
                  {USER_DATA.name}, {USER_DATA.age}
                </Text>
                <View style={styles.profileDetails}>
                  <View style={styles.detailRow}>
                    <MapPin size={16} color="rgba(255, 255, 255, 0.8)" />
                    <Text style={styles.detailText}>{USER_DATA.location}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Briefcase size={16} color="rgba(255, 255, 255, 0.8)" />
                    <Text style={styles.detailText}>{USER_DATA.occupation}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <GraduationCap size={16} color="rgba(255, 255, 255, 0.8)" />
                    <Text style={styles.detailText}>{USER_DATA.education}</Text>
                  </View>
                </View>
              </View>
            </View>

            <Text style={styles.bio}>{USER_DATA.bio}</Text>

            <View style={styles.interestsContainer}>
              <Text style={styles.interestsTitle}>Interests</Text>
              <View style={styles.interestsGrid}>
                {USER_DATA.interests.map((interest, index) => (
                  <View key={index} style={styles.interestTag}>
                    <Text style={styles.interestText}>{interest}</Text>
                  </View>
                ))}
              </View>
            </View>

            <TouchableOpacity 
              style={styles.editProfileButton}
              onPress={() => handleSettingPress('edit_profile')}
            >
              <Edit size={20} color="#8B5CF6" />
              <Text style={styles.editProfileButtonText}>Edit Profile</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.settingsContainer}>
            {SETTINGS_SECTIONS.map((section, sectionIndex) => (
              <View key={sectionIndex} style={styles.settingsSection}>
                <Text style={styles.sectionTitle}>{section.title}</Text>
                <View style={styles.sectionItems}>
                  {section.items.map(renderSettingItem)}
                </View>
              </View>
            ))}

            <View style={styles.settingsSection}>
              <View style={styles.sectionItems}>
                <TouchableOpacity
                  style={styles.logoutButton}
                  onPress={() => handleSettingPress('logout')}
                >
                  <LogOut size={20} color="#FF4458" />
                  <Text style={styles.logoutButtonText}>Sign Out</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>SoulSync v1.0.0</Text>
            <Text style={styles.footerSubtext}>Made with ❤️ for meaningful connections</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  settingsButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    padding: 8,
  },
  profileCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    margin: 24,
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#42DDA6',
    borderRadius: 12,
    padding: 4,
    borderWidth: 2,
    borderColor: 'white',
  },
  premiumBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    padding: 4,
  },
  profileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  profileName: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
  },
  profileDetails: {
    gap: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  bio: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 24,
    marginBottom: 20,
  },
  interestsContainer: {
    marginBottom: 20,
  },
  interestsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 12,
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  interestTag: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  interestText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  editProfileButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#8B5CF6',
  },
  settingsContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  settingsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 12,
  },
  sectionItems: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  highlightedSetting: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  highlightedIcon: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 8,
    padding: 4,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
  highlightedLabel: {
    color: '#FFD700',
    fontFamily: 'Inter-SemiBold',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  logoutButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FF4458',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.5)',
  },
});