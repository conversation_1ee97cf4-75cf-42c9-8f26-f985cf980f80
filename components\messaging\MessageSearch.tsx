import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { Search, X, Calendar, User } from 'lucide-react-native';
import { Message, User as UserType } from '@/types/messaging';

interface MessageSearchProps {
  visible: boolean;
  messages: Message[];
  users: UserType[];
  onClose: () => void;
  onMessageSelect: (message: Message) => void;
}

interface SearchResult extends Message {
  senderName: string;
}

export default function MessageSearch({
  visible,
  messages,
  users,
  onClose,
  onMessageSelect,
}: MessageSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchType, setSearchType] = useState<'all' | 'text' | 'media'>('all');

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = messages
        .filter(message => {
          const matchesQuery = message.content.toLowerCase().includes(searchQuery.toLowerCase());
          const matchesType = searchType === 'all' || 
            (searchType === 'text' && message.type === 'text') ||
            (searchType === 'media' && ['image', 'file', 'voice', 'video'].includes(message.type));
          
          return matchesQuery && matchesType;
        })
        .map(message => {
          const sender = users.find(u => u.id === message.senderId);
          return {
            ...message,
            senderName: sender?.name || 'Unknown',
          };
        })
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      setSearchResults(filtered);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, messages, users, searchType]);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, index) => (
      <Text
        key={index}
        style={part.toLowerCase() === query.toLowerCase() ? styles.highlightedText : null}
      >
        {part}
      </Text>
    ));
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      style={styles.resultItem}
      onPress={() => onMessageSelect(item)}
    >
      <View style={styles.resultHeader}>
        <View style={styles.senderInfo}>
          <User size={16} color="#6B7280" />
          <Text style={styles.senderName}>{item.senderName}</Text>
        </View>
        <View style={styles.dateInfo}>
          <Calendar size={16} color="#6B7280" />
          <Text style={styles.resultDate}>{formatDate(item.timestamp)}</Text>
        </View>
      </View>
      
      <Text style={styles.resultContent} numberOfLines={2}>
        {item.type === 'text' 
          ? highlightText(item.content, searchQuery)
          : `📎 ${item.type.charAt(0).toUpperCase() + item.type.slice(1)}`
        }
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search messages..."
              placeholderTextColor="#9CA3AF"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            <TouchableOpacity onPress={onClose}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterContainer}>
            <TouchableOpacity
              style={[styles.filterButton, searchType === 'all' && styles.filterButtonActive]}
              onPress={() => setSearchType('all')}
            >
              <Text style={[styles.filterText, searchType === 'all' && styles.filterTextActive]}>
                All
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterButton, searchType === 'text' && styles.filterButtonActive]}
              onPress={() => setSearchType('text')}
            >
              <Text style={[styles.filterText, searchType === 'text' && styles.filterTextActive]}>
                Text
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterButton, searchType === 'media' && styles.filterButtonActive]}
              onPress={() => setSearchType('media')}
            >
              <Text style={[styles.filterText, searchType === 'media' && styles.filterTextActive]}>
                Media
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.content}>
          {searchQuery.trim() ? (
            searchResults.length > 0 ? (
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.resultsList}
              />
            ) : (
              <View style={styles.emptyState}>
                <Search size={64} color="#D1D5DB" />
                <Text style={styles.emptyTitle}>No results found</Text>
                <Text style={styles.emptySubtitle}>
                  Try searching with different keywords
                </Text>
              </View>
            )
          ) : (
            <View style={styles.emptyState}>
              <Search size={64} color="#D1D5DB" />
              <Text style={styles.emptyTitle}>Search Messages</Text>
              <Text style={styles.emptySubtitle}>
                Enter keywords to search through your conversation history
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
    gap: 12,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  filterContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  filterButtonActive: {
    backgroundColor: '#8B5CF6',
  },
  filterText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  filterTextActive: {
    color: 'white',
  },
  content: {
    flex: 1,
  },
  resultsList: {
    padding: 16,
  },
  resultItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  senderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  senderName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  resultDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  resultContent: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  highlightedText: {
    backgroundColor: '#FEF3C7',
    fontFamily: 'Inter-SemiBold',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
});