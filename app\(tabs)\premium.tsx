import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Crown, 
  Heart, 
  Eye, 
  Zap, 
  MessageCircle, 
  Shield, 
  Star,
  Check,
  X
} from 'lucide-react-native';

const PREMIUM_FEATURES = [
  {
    icon: Eye,
    title: 'See Who Likes You',
    description: 'View all your likes instantly without swiping',
  },
  {
    icon: Heart,
    title: 'Unlimited Likes',
    description: 'Like as many profiles as you want',
  },
  {
    icon: Zap,
    title: '5 Super Likes Daily',
    description: 'Stand out with Super Likes to get noticed',
  },
  {
    icon: MessageCircle,
    title: 'Priority Messages',
    description: 'Your messages appear first in their inbox',
  },
  {
    icon: Shield,
    title: 'Advanced Filters',
    description: 'Filter by education, lifestyle, and more',
  },
  {
    icon: Star,
    title: 'Profile Boost',
    description: 'Be seen by 10x more people for 30 minutes',
  },
];

const SUBSCRIPTION_PLANS = [
  {
    id: '1_month',
    duration: '1 Month',
    price: '$29.99',
    pricePerMonth: '$29.99/month',
    savings: null,
    popular: false,
  },
  {
    id: '3_months',
    duration: '3 Months',
    price: '$59.99',
    pricePerMonth: '$19.99/month',
    savings: 'Save 33%',
    popular: true,
  },
  {
    id: '6_months',
    duration: '6 Months',
    price: '$89.99',
    pricePerMonth: '$14.99/month',
    savings: 'Save 50%',
    popular: false,
  },
];

export default function PremiumTab() {
  const [selectedPlan, setSelectedPlan] = useState('3_months');

  const handleSubscribe = () => {
    Alert.alert(
      'RevenueCat Integration Required',
      'To implement subscriptions, you\'ll need to export this project and integrate RevenueCat SDK locally. RevenueCat handles mobile subscriptions and in-app purchases seamlessly.',
      [
        { text: 'Learn More', onPress: () => console.log('Open RevenueCat docs') },
        { text: 'OK', style: 'default' },
      ]
    );
  };

  const renderFeature = (feature: any, index: number) => {
    const IconComponent = feature.icon;
    return (
      <View key={index} style={styles.featureCard}>
        <View style={styles.featureIcon}>
          <IconComponent size={24} color="#FFD700" />
        </View>
        <View style={styles.featureContent}>
          <Text style={styles.featureTitle}>{feature.title}</Text>
          <Text style={styles.featureDescription}>{feature.description}</Text>
        </View>
      </View>
    );
  };

  const renderPlan = (plan: any) => (
    <TouchableOpacity
      key={plan.id}
      style={[
        styles.planCard,
        selectedPlan === plan.id && styles.selectedPlan,
        plan.popular && styles.popularPlan,
      ]}
      onPress={() => setSelectedPlan(plan.id)}
    >
      {plan.popular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
        </View>
      )}

      <View style={styles.planHeader}>
        <Text style={styles.planDuration}>{plan.duration}</Text>
        {plan.savings && (
          <Text style={styles.planSavings}>{plan.savings}</Text>
        )}
      </View>

      <Text style={styles.planPrice}>{plan.price}</Text>
      <Text style={styles.planPricePerMonth}>{plan.pricePerMonth}</Text>

      <View style={styles.planSelector}>
        {selectedPlan === plan.id ? (
          <Check size={20} color="#42DDA6" />
        ) : (
          <View style={styles.unselectedCircle} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <View style={styles.crownContainer}>
              <Crown size={48} color="#FFD700" />
            </View>
            <Text style={styles.title}>SoulSync Premium</Text>
            <Text style={styles.subtitle}>
              Unlock premium features and find your perfect match faster
            </Text>
          </View>

          <View style={styles.featuresSection}>
            <Text style={styles.sectionTitle}>Premium Features</Text>
            <View style={styles.featuresContainer}>
              {PREMIUM_FEATURES.map(renderFeature)}
            </View>
          </View>

          <View style={styles.plansSection}>
            <Text style={styles.sectionTitle}>Choose Your Plan</Text>
            <View style={styles.plansContainer}>
              {SUBSCRIPTION_PLANS.map(renderPlan)}
            </View>
          </View>

          <View style={styles.comparisonSection}>
            <Text style={styles.sectionTitle}>Free vs Premium</Text>
            <View style={styles.comparisonTable}>
              <View style={styles.comparisonRow}>
                <Text style={styles.comparisonFeature}>Daily Likes</Text>
                <View style={styles.comparisonValues}>
                  <Text style={styles.freeValue}>10</Text>
                  <Text style={styles.premiumValue}>Unlimited</Text>
                </View>
              </View>
              
              <View style={styles.comparisonRow}>
                <Text style={styles.comparisonFeature}>Super Likes</Text>
                <View style={styles.comparisonValues}>
                  <Text style={styles.freeValue}>1/day</Text>
                  <Text style={styles.premiumValue}>5/day</Text>
                </View>
              </View>

              <View style={styles.comparisonRow}>
                <Text style={styles.comparisonFeature}>See Who Likes You</Text>
                <View style={styles.comparisonValues}>
                  <X size={16} color="#FF4458" />
                  <Check size={16} color="#42DDA6" />
                </View>
              </View>

              <View style={styles.comparisonRow}>
                <Text style={styles.comparisonFeature}>Profile Boost</Text>
                <View style={styles.comparisonValues}>
                  <X size={16} color="#FF4458" />
                  <Check size={16} color="#42DDA6" />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.subscribeButton} onPress={handleSubscribe}>
            <Crown size={20} color="white" />
            <Text style={styles.subscribeButtonText}>
              Subscribe for {SUBSCRIPTION_PLANS.find(p => p.id === selectedPlan)?.pricePerMonth}
            </Text>
          </TouchableOpacity>
          
          <View style={styles.footerLinks}>
            <TouchableOpacity>
              <Text style={styles.footerLink}>Terms of Service</Text>
            </TouchableOpacity>
            <Text style={styles.footerSeparator}>•</Text>
            <TouchableOpacity>
              <Text style={styles.footerLink}>Privacy Policy</Text>
            </TouchableOpacity>
            <Text style={styles.footerSeparator}>•</Text>
            <TouchableOpacity>
              <Text style={styles.footerLink}>Restore Purchase</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  crownContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24,
  },
  featuresSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: 'white',
    marginBottom: 16,
  },
  featuresContainer: {
    gap: 16,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
  },
  featureIcon: {
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
  },
  plansSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  plansContainer: {
    gap: 12,
  },
  planCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  selectedPlan: {
    borderColor: '#42DDA6',
    backgroundColor: 'rgba(66, 221, 166, 0.1)',
  },
  popularPlan: {
    borderColor: '#FFD700',
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    backgroundColor: '#FFD700',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  popularBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#000',
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  planDuration: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  planSavings: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#42DDA6',
    backgroundColor: 'rgba(66, 221, 166, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  planPrice: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 4,
  },
  planPricePerMonth: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  planSelector: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  unselectedCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  comparisonSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  comparisonTable: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
  },
  comparisonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  comparisonFeature: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'white',
    flex: 1,
  },
  comparisonValues: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 32,
  },
  freeValue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    width: 60,
    textAlign: 'center',
  },
  premiumValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#42DDA6',
    width: 60,
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFD700',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 16,
    gap: 8,
  },
  subscribeButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  footerLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  footerLink: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  footerSeparator: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.5)',
  },
});