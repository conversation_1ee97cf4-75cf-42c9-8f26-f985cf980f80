import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
} from 'react-native';
import { Heart, ThumbsUp, Laugh, Angry, Frown, Plus } from 'lucide-react-native';

interface MessageReactionsProps {
  visible: boolean;
  onClose: () => void;
  onReactionSelect: (reaction: string) => void;
  messageId: string;
}

const REACTIONS = [
  { emoji: '❤️', icon: Heart, color: '#EF4444' },
  { emoji: '👍', icon: ThumbsUp, color: '#3B82F6' },
  { emoji: '😂', icon: Laugh, color: '#F59E0B' },
  { emoji: '😢', icon: Frown, color: '#6B7280' },
  { emoji: '😡', icon: Angry, color: '#DC2626' },
];

export default function MessageReactions({
  visible,
  onClose,
  onReactionSelect,
  messageId,
}: MessageReactionsProps) {
  const [scaleAnim] = useState(new Animated.Value(0));

  React.useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      scaleAnim.setValue(0);
    }
  }, [visible, scaleAnim]);

  const handleReactionSelect = (emoji: string) => {
    onReactionSelect(emoji);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.reactionsContainer}>
            {REACTIONS.map((reaction, index) => {
              const IconComponent = reaction.icon;
              return (
                <TouchableOpacity
                  key={index}
                  style={[styles.reactionButton, { backgroundColor: reaction.color }]}
                  onPress={() => handleReactionSelect(reaction.emoji)}
                >
                  <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
                </TouchableOpacity>
              );
            })}
            
            <TouchableOpacity style={styles.moreButton} onPress={onClose}>
              <Plus size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 30,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  reactionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  reactionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reactionEmoji: {
    fontSize: 20,
  },
  moreButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
});