import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { Heart, ThumbsUp, Laugh, Angry, Frown, Plus, X } from 'lucide-react-native';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface MessageReaction {
  emoji: string;
  users: string[];
  count: number;
}

interface MessageReactionsProps {
  messageId: string;
  reactions?: MessageReaction[];
  currentUserId: string;
  onAddReaction: (messageId: string, emoji: string) => void;
  onRemoveReaction: (messageId: string, emoji: string) => void;
  onShowReactionDetails?: (messageId: string, emoji: string) => void;
  // Legacy props for backward compatibility
  visible?: boolean;
  onClose?: () => void;
  onReactionSelect?: (reaction: string) => void;
}

const QUICK_REACTIONS = ['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'];
const { width } = Dimensions.get('window');

const REACTIONS = [
  { emoji: '❤️', icon: Heart, color: '#EF4444' },
  { emoji: '👍', icon: ThumbsUp, color: '#3B82F6' },
  { emoji: '😂', icon: Laugh, color: '#F59E0B' },
  { emoji: '😢', icon: Frown, color: '#6B7280' },
  { emoji: '😡', icon: Angry, color: '#DC2626' },
];

export default function MessageReactions({
  messageId,
  reactions = [],
  currentUserId,
  onAddReaction,
  onRemoveReaction,
  onShowReactionDetails,
  // Legacy props
  visible,
  onClose,
  onReactionSelect,
}: MessageReactionsProps) {
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  // Legacy support
  if (visible !== undefined && onClose && onReactionSelect) {
    return <LegacyReactionPicker
      visible={visible}
      onClose={onClose}
      onReactionSelect={onReactionSelect}
      messageId={messageId}
    />;
  }

  const handleReactionPress = (emoji: string) => {
    const reaction = reactions.find(r => r.emoji === emoji);
    const userHasReacted = reaction?.users.includes(currentUserId);

    // Haptic feedback
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Animation
    scale.value = withSpring(0.8, { duration: 100 }, () => {
      scale.value = withSpring(1);
    });

    if (userHasReacted) {
      onRemoveReaction(messageId, emoji);
    } else {
      onAddReaction(messageId, emoji);
    }
  };

  const handleLongPress = (emoji: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    onShowReactionDetails?.(messageId, emoji);
  };

  const openReactionPicker = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setShowReactionPicker(true);
    opacity.value = withTiming(1, { duration: 200 });
  };

  const closeReactionPicker = () => {
    opacity.value = withTiming(0, { duration: 200 }, () => {
      runOnJS(setShowReactionPicker)(false);
    });
  };

  const handleQuickReaction = (emoji: string) => {
    handleReactionPress(emoji);
    closeReactionPicker();
  };

  if (reactions.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.reactionsContainer}
      >
        {reactions.map((reaction) => {
          const userHasReacted = reaction.users.includes(currentUserId);
          return (
            <TouchableOpacity
              key={reaction.emoji}
              style={[
                styles.reactionBubble,
                userHasReacted && styles.userReactionBubble,
              ]}
              onPress={() => handleReactionPress(reaction.emoji)}
              onLongPress={() => handleLongPress(reaction.emoji)}
              activeOpacity={0.7}
            >
              <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
              <Text style={[
                styles.reactionCount,
                userHasReacted && styles.userReactionCount,
              ]}>
                {reaction.count}
              </Text>
            </TouchableOpacity>
          );
        })}

        <TouchableOpacity
          style={styles.addReactionButton}
          onPress={openReactionPicker}
          activeOpacity={0.7}
        >
          <Plus size={14} color={theme.colors.gray500} />
        </TouchableOpacity>
      </ScrollView>

      {/* Reaction Picker Modal */}
      <Modal
        visible={showReactionPicker}
        transparent
        animationType="none"
        onRequestClose={closeReactionPicker}
      >
        <BlurView intensity={20} style={styles.modalOverlay}>
          <Animated.View style={[styles.reactionPicker, animatedStyle]}>
            <View style={styles.pickerHeader}>
              <Text style={styles.pickerTitle}>Add Reaction</Text>
              <TouchableOpacity
                onPress={closeReactionPicker}
                style={styles.closeButton}
              >
                <X size={20} color={theme.colors.gray600} />
              </TouchableOpacity>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.quickReactions}
            >
              {QUICK_REACTIONS.map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  style={styles.quickReactionButton}
                  onPress={() => handleQuickReaction(emoji)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.quickReactionEmoji}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Animated.View>
        </BlurView>
      </Modal>
    </View>
  );
}

// Legacy component for backward compatibility
function LegacyReactionPicker({
  visible,
  onClose,
  onReactionSelect,
  messageId,
}: {
  visible: boolean;
  onClose: () => void;
  onReactionSelect: (reaction: string) => void;
  messageId: string;
}) {
  const [scaleAnim] = useState(new Animated.Value(0));

  React.useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      scaleAnim.setValue(0);
    }
  }, [visible, scaleAnim]);

  const handleReactionSelect = (emoji: string) => {
    onReactionSelect(emoji);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <Animated.View
          style={[
            styles.legacyContainer,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.legacyReactionsContainer}>
            {REACTIONS.map((reaction, index) => {
              return (
                <TouchableOpacity
                  key={index}
                  style={[styles.legacyReactionButton, { backgroundColor: reaction.color }]}
                  onPress={() => handleReactionSelect(reaction.emoji)}
                >
                  <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
                </TouchableOpacity>
              );
            })}

            <TouchableOpacity style={styles.moreButton} onPress={onClose}>
              <Plus size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 4,
  },
  reactionsContainer: {
    flexDirection: 'row',
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  userReactionBubble: {
    backgroundColor: theme.colors.primary + '20',
    borderColor: theme.colors.primary,
  },
  reactionEmoji: {
    fontSize: 14,
    marginRight: 4,
  },
  reactionCount: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
  },
  userReactionCount: {
    color: theme.colors.primary,
  },
  addReactionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  reactionPicker: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: width * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  pickerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.gray900,
  },
  closeButton: {
    padding: 4,
  },
  quickReactions: {
    flexDirection: 'row',
  },
  quickReactionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.gray50,
    marginRight: 12,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  quickReactionEmoji: {
    fontSize: 24,
  },
  // Legacy styles
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  legacyContainer: {
    backgroundColor: 'white',
    borderRadius: 30,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  legacyReactionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legacyReactionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
});