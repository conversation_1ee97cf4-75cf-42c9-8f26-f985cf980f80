import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { MessageCircle, Clock } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { theme } from '@/constants/theme';
import { formatTimeAgo } from '@/utils/dateUtils';

interface MatchData {
  id: string;
  otherUser: {
    id: string;
    name: string;
    age: number;
    photo: string;
    lastMessage?: string;
    lastMessageTime?: string;
    unread?: boolean;
  };
  timestamp: Date;
  status: 'active' | 'expired' | 'blocked';
}

interface AnimatedMatchCardProps {
  match: MatchData;
  onPress: (matchId: string) => void;
}

const triggerHaptic = () => {
  if (Platform.OS !== 'web') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }
};

export default function AnimatedMatchCard({ match, onPress }: AnimatedMatchCardProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const handlePress = () => {
    triggerHaptic();
    
    scale.value = withSpring(0.95, { duration: 150 }, () => {
      scale.value = withSpring(1, { duration: 150 });
    });
    
    runOnJS(onPress)(match.id);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  // Using the utility function for consistent date handling

  const getStatusColor = () => {
    switch (match.status) {
      case 'active':
        return theme.colors.online;
      case 'expired':
        return theme.colors.warning;
      case 'blocked':
        return theme.colors.error;
      default:
        return theme.colors.gray400;
    }
  };

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <TouchableOpacity 
        onPress={handlePress} 
        activeOpacity={0.9}
        style={styles.touchable}
      >
        <View style={styles.card}>
          {/* Profile Photo */}
          <View style={styles.photoContainer}>
            <Image 
              source={{ uri: match.otherUser.photo }} 
              style={styles.photo} 
            />
            {/* Online Status Indicator */}
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
          </View>

          {/* Match Info */}
          <View style={styles.info}>
            <View style={styles.header}>
              <Text style={styles.name}>
                {match.otherUser.name}, {match.otherUser.age}
              </Text>
              {match.otherUser.lastMessageTime && (
                <View style={styles.timeContainer}>
                  <Clock size={12} color="rgba(255, 255, 255, 0.6)" />
                  <Text style={styles.time}>
                    {formatTimeAgo(match.otherUser.lastMessageTime)}
                  </Text>
                </View>
              )}
            </View>
            
            {match.otherUser.lastMessage ? (
              <Text 
                style={[
                  styles.message, 
                  match.otherUser.unread && styles.unreadMessage
                ]}
                numberOfLines={1}
              >
                {match.otherUser.lastMessage}
              </Text>
            ) : (
              <Text style={styles.noMessage}>
                Say hello to your new match! 👋
              </Text>
            )}
          </View>

          {/* Message Icon */}
          <View style={styles.messageIcon}>
            <MessageCircle 
              size={20} 
              color={match.otherUser.unread ? theme.colors.primary : 'rgba(255, 255, 255, 0.6)'} 
            />
          </View>

          {/* Unread Indicator */}
          {match.otherUser.unread && (
            <View style={styles.unreadDot} />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  touchable: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    ...theme.shadows.sm,
  },
  photoContainer: {
    position: 'relative',
  },
  photo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.gray300,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  info: {
    flex: 1,
    marginLeft: 16,
    marginRight: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    flex: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  time: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginLeft: 4,
  },
  message: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 18,
  },
  unreadMessage: {
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  noMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    fontStyle: 'italic',
  },
  messageIcon: {
    padding: 8,
  },
  unreadDot: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary,
  },
});
