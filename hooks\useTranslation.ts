import { useState, useEffect, useCallback } from 'react';
import { translationService, TranslationResult, TranslationSettings } from '@/services/translationService';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface TranslationState {
  isTranslating: boolean;
  translations: { [messageId: string]: TranslationResult };
  settings: TranslationSettings;
  error: string | null;
}

export function useTranslation() {
  const [state, setState] = useState<TranslationState>({
    isTranslating: false,
    translations: {},
    settings: {
      autoTranslate: false,
      targetLanguage: 'en',
      showOriginal: true,
    },
    error: null,
  });

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const settings = await translationService.getSettings();
      setState(prev => ({ ...prev, settings }));
    } catch (error) {
      console.error('Failed to load translation settings:', error);
    }
  };

  const saveSettings = async (newSettings: Partial<TranslationSettings>) => {
    try {
      const updatedSettings = { ...state.settings, ...newSettings };
      await translationService.saveSettings(updatedSettings);
      setState(prev => ({ ...prev, settings: updatedSettings }));
      
      // Haptic feedback
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to save settings' 
      }));
    }
  };

  const translateMessage = useCallback(async (
    messageId: string,
    text: string,
    targetLanguage?: string
  ): Promise<TranslationResult | null> => {
    if (state.translations[messageId]) {
      return state.translations[messageId];
    }

    setState(prev => ({ ...prev, isTranslating: true, error: null }));

    try {
      const result = await translationService.translateText(
        text,
        targetLanguage || state.settings.targetLanguage
      );

      setState(prev => ({
        ...prev,
        translations: {
          ...prev.translations,
          [messageId]: result,
        },
        isTranslating: false,
      }));

      // Haptic feedback on successful translation
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isTranslating: false,
        error: error instanceof Error ? error.message : 'Translation failed',
      }));
      return null;
    }
  }, [state.settings.targetLanguage, state.translations]);

  const toggleAutoTranslate = useCallback(async () => {
    await saveSettings({ autoTranslate: !state.settings.autoTranslate });
  }, [state.settings.autoTranslate, saveSettings]);

  const setTargetLanguage = useCallback(async (language: string) => {
    await saveSettings({ targetLanguage: language });
    
    // Clear existing translations when language changes
    setState(prev => ({ ...prev, translations: {} }));
  }, [saveSettings]);

  const toggleShowOriginal = useCallback(async () => {
    await saveSettings({ showOriginal: !state.settings.showOriginal });
  }, [state.settings.showOriginal, saveSettings]);

  const getTranslation = useCallback((messageId: string): TranslationResult | null => {
    return state.translations[messageId] || null;
  }, [state.translations]);

  const clearTranslations = useCallback(() => {
    setState(prev => ({ ...prev, translations: {} }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const detectLanguage = useCallback(async (text: string) => {
    try {
      return await translationService.detectLanguage(text);
    } catch (error) {
      console.error('Language detection failed:', error);
      return null;
    }
  }, []);

  const getSupportedLanguages = useCallback(() => {
    return translationService.getSupportedLanguages();
  }, []);

  const isLanguageSupported = useCallback((languageCode: string) => {
    return translationService.isLanguageSupported(languageCode);
  }, []);

  return {
    // State
    isTranslating: state.isTranslating,
    translations: state.translations,
    settings: state.settings,
    error: state.error,
    
    // Actions
    translateMessage,
    toggleAutoTranslate,
    setTargetLanguage,
    toggleShowOriginal,
    getTranslation,
    clearTranslations,
    clearError,
    detectLanguage,
    
    // Utilities
    getSupportedLanguages,
    isLanguageSupported,
  };
}
