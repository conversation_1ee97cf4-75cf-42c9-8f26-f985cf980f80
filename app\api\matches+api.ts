import { Match } from '@/types/messaging';

// Mock data for matches
const MOCK_MATCHES: (Match & { 
  otherUser: {
    id: string;
    name: string;
    age: number;
    photo: string;
    lastMessage?: string;
    lastMessageTime?: string;
    unread?: boolean;
  }
})[] = [
  {
    id: 'match_1',
    users: ['current-user', 'user_1'],
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    status: 'active',
    otherUser: {
      id: 'user_1',
      name: '<PERSON>',
      age: 26,
      photo: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Hey! Thanks for the like 😊',
      lastMessageTime: '2 hours ago',
      unread: true,
    },
  },
  {
    id: 'match_2',
    users: ['current-user', 'user_2'],
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    status: 'active',
    otherUser: {
      id: 'user_2',
      name: '<PERSON>',
      age: 24,
      photo: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Would love to grab coffee sometime!',
      lastMessageTime: '1 day ago',
      unread: false,
    },
  },
  {
    id: 'match_3',
    users: ['current-user', 'user_3'],
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    status: 'active',
    otherUser: {
      id: 'user_3',
      name: 'Emma',
      age: 28,
      photo: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'That hiking trail looks amazing!',
      lastMessageTime: '3 days ago',
      unread: false,
    },
  },
  {
    id: 'match_4',
    users: ['current-user', 'user_4'],
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    status: 'active',
    otherUser: {
      id: 'user_4',
      name: 'Isabella',
      age: 30,
      photo: 'https://images.pexels.com/photos/1547971/pexels-photo-1547971.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Your art collection is incredible!',
      lastMessageTime: '5 days ago',
      unread: false,
    },
  },
];

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMatches = MOCK_MATCHES.slice(startIndex, endIndex);
    const hasMore = endIndex < MOCK_MATCHES.length;
    
    return Response.json({
      success: true,
      matches: paginatedMatches,
      hasMore,
      total: MOCK_MATCHES.length,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching matches:', error);
    return Response.json(
      { 
        success: false, 
        error: 'Failed to fetch matches' 
      },
      { status: 500 }
    );
  }
}
